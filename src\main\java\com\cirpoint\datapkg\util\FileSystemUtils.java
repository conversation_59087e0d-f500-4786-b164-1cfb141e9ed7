package com.cirpoint.datapkg.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 文件系统操作工具类
 * 提供目录扫描、文件复制、目录联接、磁盘空间检查等功能
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Component
public class FileSystemUtils {
    
    private static final Pattern MONTH_PATTERN = Pattern.compile("^\\d{4}-\\d{2}$");
    
    /**
     * 扫描目录下的月份目录（yyyy-MM格式）
     * 
     * @param sourceDir 源目录
     * @return 月份目录列表，按名称排序
     */
    public List<String> scanMonthDirectories(Path sourceDir) {
        log.info("扫描月份目录: {}", sourceDir);
        
        if (!Files.exists(sourceDir) || !Files.isDirectory(sourceDir)) {
            log.warn("源目录不存在或不是目录: {}", sourceDir);
            return Collections.emptyList();
        }
        
        try {
            return Files.list(sourceDir)
                    .filter(Files::isDirectory)
                    .map(path -> path.getFileName().toString())
                    .filter(name -> MONTH_PATTERN.matcher(name).matches())
                    .sorted()
                    .collect(Collectors.toList());
        } catch (IOException e) {
            log.error("扫描月份目录失败: {}", sourceDir, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 过滤月份列表，排除最近N个月
     * 
     * @param allMonths 所有月份列表
     * @param excludeRecentMonths 排除最近N个月
     * @return 过滤后的月份列表
     */
    public List<String> filterMonths(List<String> allMonths, int excludeRecentMonths) {
        if (allMonths.isEmpty() || excludeRecentMonths <= 0) {
            return new ArrayList<>(allMonths);
        }
        
        int totalMonths = allMonths.size();
        int keepCount = Math.max(0, totalMonths - excludeRecentMonths);
        
        log.info("总月份数: {}, 排除最近: {}个月, 保留: {}个月", 
                totalMonths, excludeRecentMonths, keepCount);
        
        return allMonths.subList(0, keepCount);
    }
    
    /**
     * 获取磁盘可用空间（GB）
     * 
     * @param path 磁盘路径
     * @return 可用空间（GB），失败返回0
     */
    public long getDiskFreeSpaceGB(Path path) {
        try {
            File file = path.toFile();
            long freeBytes = file.getFreeSpace();
            long freeGB = freeBytes / (1024L * 1024L * 1024L);
            log.debug("磁盘 {} 可用空间: {}GB", path, freeGB);
            return freeGB;
        } catch (Exception e) {
            log.error("获取磁盘空间失败: {}", path, e);
            return 0;
        }
    }
    
    /**
     * 检查路径是否为NTFS目录联接
     * 
     * @param path 路径
     * @return true如果是目录联接
     */
    public boolean isJunction(Path path) {
        if (!Files.exists(path)) {
            return false;
        }
        
        try {
            // 在Windows上使用dir命令检查是否为JUNCTION
            ProcessBuilder pb = new ProcessBuilder("cmd", "/c", "dir", "\"" + path.toString() + "\"");
            Process process = pb.start();
            
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), "GBK"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("<JUNCTION>")) {
                        return true;
                    }
                }
            }
            
            process.waitFor();
            return false;
        } catch (Exception e) {
            log.error("检查目录联接失败: {}", path, e);
            return false;
        }
    }
    
    /**
     * 创建NTFS目录联接
     * 
     * @param linkPath 联接路径
     * @param targetPath 目标路径
     * @param dryRun 是否为演练模式
     * @return 是否成功
     */
    public boolean createJunction(Path linkPath, Path targetPath, boolean dryRun) {
        String command = String.format("mklink /J \"%s\" \"%s\"", linkPath, targetPath);
        log.info("创建目录联接: {}", command);
        
        if (dryRun) {
            log.info("[演练模式] 将执行: {}", command);
            return true;
        }
        
        try {
            ProcessBuilder pb = new ProcessBuilder("cmd", "/c", command);
            Process process = pb.start();
            
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                log.info("目录联接创建成功: {} -> {}", linkPath, targetPath);
                return true;
            } else {
                log.error("目录联接创建失败，退出码: {}", exitCode);
                return false;
            }
        } catch (Exception e) {
            log.error("创建目录联接失败: {} -> {}", linkPath, targetPath, e);
            return false;
        }
    }
    
    /**
     * 删除目录联接
     * 
     * @param linkPath 联接路径
     * @param dryRun 是否为演练模式
     * @return 是否成功
     */
    public boolean removeJunction(Path linkPath, boolean dryRun) {
        String command = String.format("rmdir \"%s\"", linkPath);
        log.info("删除目录联接: {}", command);
        
        if (dryRun) {
            log.info("[演练模式] 将执行: {}", command);
            return true;
        }
        
        try {
            ProcessBuilder pb = new ProcessBuilder("cmd", "/c", command);
            Process process = pb.start();
            
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                log.info("目录联接删除成功: {}", linkPath);
                return true;
            } else {
                log.error("目录联接删除失败，退出码: {}", exitCode);
                return false;
            }
        } catch (Exception e) {
            log.error("删除目录联接失败: {}", linkPath, e);
            return false;
        }
    }
    
    /**
     * 使用Robocopy复制文件
     * 
     * @param source 源路径
     * @param target 目标路径
     * @param options Robocopy选项
     * @param logFile 日志文件
     * @param dryRun 是否为演练模式
     * @return 是否成功
     */
    public boolean robocopy(Path source, Path target, String options, Path logFile, boolean dryRun) {
        // 确保目标目录存在
        if (!dryRun) {
            try {
                Files.createDirectories(target);
            } catch (IOException e) {
                log.error("创建目标目录失败: {}", target, e);
                return false;
            }
        }
        
        String command = String.format("robocopy \"%s\" \"%s\" %s /LOG+:\"%s\"", 
                source, target, options, logFile);
        log.info("执行Robocopy: {}", command);
        
        if (dryRun) {
            log.info("[演练模式] 将执行: {}", command);
            return true;
        }
        
        try {
            ProcessBuilder pb = new ProcessBuilder("cmd", "/c", command);
            Process process = pb.start();
            
            int exitCode = process.waitFor();
            // Robocopy的返回码：0-1表示成功，>=8表示错误
            boolean success = exitCode < 8;
            
            if (success) {
                log.info("Robocopy执行成功，退出码: {}", exitCode);
            } else {
                log.error("Robocopy执行失败，退出码: {}", exitCode);
            }
            
            return success;
        } catch (Exception e) {
            log.error("执行Robocopy失败: {} -> {}", source, target, e);
            return false;
        }
    }
    
    /**
     * 重命名目录
     * 
     * @param oldPath 原路径
     * @param newPath 新路径
     * @param dryRun 是否为演练模式
     * @return 是否成功
     */
    public boolean renameDirectory(Path oldPath, Path newPath, boolean dryRun) {
        log.info("重命名目录: {} -> {}", oldPath, newPath);
        
        if (dryRun) {
            log.info("[演练模式] 将重命名: {} -> {}", oldPath, newPath);
            return true;
        }
        
        try {
            Files.move(oldPath, newPath);
            log.info("目录重命名成功: {} -> {}", oldPath, newPath);
            return true;
        } catch (IOException e) {
            log.error("目录重命名失败: {} -> {}", oldPath, newPath, e);
            return false;
        }
    }
    
    /**
     * 删除目录及其内容
     *
     * @param path 目录路径
     * @param dryRun 是否为演练模式
     * @return 是否成功
     */
    public boolean deleteDirectory(Path path, boolean dryRun) {
        log.info("删除目录: {}", path);

        if (dryRun) {
            log.info("[演练模式] 将删除目录: {}", path);
            return true;
        }

        try {
            if (Files.exists(path)) {
                Files.walkFileTree(path, new SimpleFileVisitor<Path>() {
                    @Override
                    public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                        Files.delete(file);
                        return FileVisitResult.CONTINUE;
                    }

                    @Override
                    public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                        Files.delete(dir);
                        return FileVisitResult.CONTINUE;
                    }
                });
                log.info("目录删除成功: {}", path);
            } else {
                log.info("目录不存在，无需删除: {}", path);
            }
            return true;
        } catch (IOException e) {
            log.error("删除目录失败: {}", path, e);
            return false;
        }
    }

    /**
     * 计算文件SHA256哈希值
     *
     * @param filePath 文件路径
     * @return SHA256哈希值，失败返回null
     */
    public String calculateSHA256(Path filePath) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] fileBytes = Files.readAllBytes(filePath);
            byte[] hashBytes = digest.digest(fileBytes);

            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString().toUpperCase();
        } catch (NoSuchAlgorithmException | IOException e) {
            log.error("计算文件哈希失败: {}", filePath, e);
            return null;
        }
    }

    /**
     * 抽样验证文件哈希
     *
     * @param sourceDir 源目录
     * @param sampleCount 抽样数量
     * @param logFile 日志文件
     * @return 验证结果
     */
    public boolean sampleHashVerify(Path sourceDir, int sampleCount, Path logFile) {
        if (sampleCount <= 0) {
            log.info("跳过哈希验证，抽样数量为0");
            return true;
        }

        log.info("开始抽样哈希验证: {}, 抽样数量: {}", sourceDir, sampleCount);

        try {
            List<Path> allFiles = Files.walk(sourceDir)
                    .filter(Files::isRegularFile)
                    .collect(Collectors.toList());

            if (allFiles.isEmpty()) {
                log.info("目录中没有文件，跳过哈希验证: {}", sourceDir);
                return true;
            }

            // 随机抽样
            Collections.shuffle(allFiles);
            List<Path> sampleFiles = allFiles.subList(0, Math.min(sampleCount, allFiles.size()));

            List<String> logLines = new ArrayList<>();
            logLines.add("抽样路径：" + sourceDir);

            boolean allSuccess = true;
            for (Path file : sampleFiles) {
                String hash = calculateSHA256(file);
                if (hash != null) {
                    logLines.add("校验：" + file);
                    logLines.add("SHA256: " + hash);
                } else {
                    logLines.add("校验失败：" + file);
                    allSuccess = false;
                }
            }

            // 写入日志文件
            Files.write(logFile, logLines, StandardOpenOption.CREATE, StandardOpenOption.WRITE);

            log.info("抽样哈希验证完成，结果: {}", allSuccess ? "成功" : "部分失败");
            return allSuccess;

        } catch (IOException e) {
            log.error("抽样哈希验证失败: {}", sourceDir, e);
            return false;
        }
    }

    /**
     * 管理Windows服务
     *
     * @param serviceName 服务名称
     * @param action 操作：start 或 stop
     * @param dryRun 是否为演练模式
     * @return 是否成功
     */
    public boolean manageService(String serviceName, String action, boolean dryRun) {
        if (serviceName == null || serviceName.trim().isEmpty()) {
            log.info("服务名称为空，跳过服务管理");
            return true;
        }

        String command = String.format("net %s \"%s\"", action, serviceName);
        log.info("管理服务: {}", command);

        if (dryRun) {
            log.info("[演练模式] 将执行: {}", command);
            return true;
        }

        try {
            ProcessBuilder pb = new ProcessBuilder("cmd", "/c", command);
            Process process = pb.start();

            int exitCode = process.waitFor();
            if (exitCode == 0) {
                log.info("服务{}成功: {}", action, serviceName);
                return true;
            } else {
                log.error("服务{}失败，退出码: {}, 服务: {}", action, exitCode, serviceName);
                return false;
            }
        } catch (Exception e) {
            log.error("管理服务失败: {} {}", action, serviceName, e);
            return false;
        }
    }

    /**
     * 创建Windows计划任务
     *
     * @param taskName 任务名称
     * @param scriptPath 脚本路径
     * @param schedule 计划表达式
     * @param dryRun 是否为演练模式
     * @return 是否成功
     */
    public boolean createScheduledTask(String taskName, String scriptPath, String schedule, boolean dryRun) {
        String command = String.format(
                "schtasks /Create /TN \"%s\" /TR \"\\\"%s\\\"\" %s /RL HIGHEST /F",
                taskName, scriptPath, schedule);
        log.info("创建计划任务: {}", command);

        if (dryRun) {
            log.info("[演练模式] 将执行: {}", command);
            return true;
        }

        try {
            ProcessBuilder pb = new ProcessBuilder("cmd", "/c", command);
            Process process = pb.start();

            int exitCode = process.waitFor();
            if (exitCode == 0) {
                log.info("计划任务创建成功: {}", taskName);
                return true;
            } else {
                log.error("计划任务创建失败，退出码: {}, 任务: {}", exitCode, taskName);
                return false;
            }
        } catch (Exception e) {
            log.error("创建计划任务失败: {}", taskName, e);
            return false;
        }
    }
}
