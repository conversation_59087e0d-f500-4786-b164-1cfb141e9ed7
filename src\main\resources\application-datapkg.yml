# 数据包迁移配置文件
# 对应原脚本：01-config.bat
# 作者：wanghq
# 日期：2025-08-27

datapkg:
  migrate:
    # 源与目标目录
    source-root: "D:\\DataPkgFile"
    target-e: "E:\\DataPkgFile"
    target-f: "F:\\DataPkgFile"
    
    # 日志目录
    log-dir: "D:\\eclipse-work\\DataPkg-Migrate\\logs"
    
    # 服务名称（可选）：如 Tomcat8.5；若无则留空
    service-name: ""
    
    # 分配策略：FILL_E_THEN_F 或 BALANCE_BY_MONTH
    strategy: "FILL_E_THEN_F"
    
    # 预留容量（GB），低于该值停止向该盘写入
    target-e-min-free-gb: 50
    target-f-min-free-gb: 50
    
    # 预同步排除最近 N 个月
    exclude-recent-months: 2
    
    # 多线程与重试
    threads: 16
    robo-retry: 1
    robo-wait: 2
    
    # 安全开关：true=演练（只打印命令），false=执行
    dry-run: false
    
    # 切换后是否删除 _old：true=删除，false=保留
    delete-old: true
    
    # 抽样验证（每月抽 N 个文件哈希校验，可设 0 跳过）
    verify-sample-count: 5

# 日志配置
logging:
  level:
    com.cirpoint.datapkg: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: "${datapkg.migrate.log-dir}/datapkg-migrate.log"
    max-size: 100MB
    max-history: 30
