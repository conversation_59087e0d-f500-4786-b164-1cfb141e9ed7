package com.cirpoint.datapkg.service;

import com.cirpoint.datapkg.config.DataPkgMigrateConfig;
import com.cirpoint.datapkg.util.FileSystemUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 计划任务和辅助功能服务
 * 对应原脚本：50-autolink-schedule.bat 和 create_current_month_link.bat
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduleService {
    
    private final DataPkgMigrateConfig config;
    private final FileSystemUtils fileSystemUtils;
    private final PreSyncService preSyncService;
    
    /**
     * 创建每月自动联接计划任务
     * 
     * @return 创建结果
     */
    public ScheduleTaskResult createAutoLinkSchedule() {
        log.info("创建每月自动联接计划任务");
        
        String taskName = "CreateMonthlyLink-DataPkg";
        String schedule = "/SC MONTHLY /D 1 /ST 00:05";
        
        // 这里应该指向一个Java程序的启动脚本，而不是原来的bat文件
        // 实际部署时需要创建对应的启动脚本
        String scriptPath = "java -jar file-handle.jar --datapkg.create-current-month-link";
        
        boolean success = fileSystemUtils.createScheduledTask(taskName, scriptPath, schedule, config.isDryRun());
        
        String message = success ? "计划任务创建成功" : "计划任务创建失败";
        
        return ScheduleTaskResult.builder()
                .success(success)
                .message(message)
                .taskName(taskName)
                .schedule("每月1日 00:05")
                .scriptPath(scriptPath)
                .build();
    }
    
    /**
     * 创建当前月份的目录联接
     * 
     * @return 创建结果
     */
    public MonthLinkResult createCurrentMonthLink() {
        return createMonthLink(null);
    }
    
    /**
     * 创建指定月份的目录联接
     * 
     * @param targetMonth 目标月份，null表示下个月
     * @return 创建结果
     */
    public MonthLinkResult createMonthLink(String targetMonth) {
        // 确定目标月份
        String month;
        if (targetMonth != null && !targetMonth.trim().isEmpty()) {
            month = targetMonth.trim();
        } else {
            // 获取下个月份
            LocalDate nextMonth = LocalDate.now().plusMonths(1);
            month = nextMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        }
        
        log.info("创建月份目录联接: {}", month);
        
        // 选择目标盘
        PreSyncService.TargetSelection targetSelection = selectTargetForNewMonth();
        if (targetSelection.getTargetPath() == null) {
            return MonthLinkResult.builder()
                    .success(false)
                    .message("无可用目标盘")
                    .month(month)
                    .build();
        }
        
        Path targetDir = targetSelection.getTargetPath().resolve(month);
        Path sourceDir = config.getSourceRootPath().resolve(month);
        
        // 创建目标目录
        if (!config.isDryRun()) {
            try {
                Files.createDirectories(targetDir);
                log.info("创建目标目录: {}", targetDir);
            } catch (Exception e) {
                log.error("创建目标目录失败: {}", targetDir, e);
                return MonthLinkResult.builder()
                        .success(false)
                        .message("创建目标目录失败: " + e.getMessage())
                        .month(month)
                        .targetDir(targetDir.toString())
                        .build();
            }
        }
        
        // 检查源目录状态
        if (Files.exists(sourceDir)) {
            if (fileSystemUtils.isJunction(sourceDir)) {
                log.info("联接已存在: {} -> {}", sourceDir, targetDir);
                return MonthLinkResult.builder()
                        .success(true)
                        .message("联接已存在")
                        .month(month)
                        .sourceDir(sourceDir.toString())
                        .targetDir(targetDir.toString())
                        .targetDrive(targetSelection.getDrive())
                        .alreadyExists(true)
                        .build();
            } else {
                log.warn("发现实体目录，需要先迁移数据: {}", sourceDir);
                return MonthLinkResult.builder()
                        .success(false)
                        .message("发现实体目录，需要先迁移数据")
                        .month(month)
                        .sourceDir(sourceDir.toString())
                        .targetDir(targetDir.toString())
                        .targetDrive(targetSelection.getDrive())
                        .needsMigration(true)
                        .build();
            }
        }
        
        // 创建目录联接
        boolean success = fileSystemUtils.createJunction(sourceDir, targetDir, config.isDryRun());
        String message = success ? "联接创建成功" : "联接创建失败";
        
        log.info("月份 {} 联接创建结果: {}", month, message);
        
        return MonthLinkResult.builder()
                .success(success)
                .message(message)
                .month(month)
                .sourceDir(sourceDir.toString())
                .targetDir(targetDir.toString())
                .targetDrive(targetSelection.getDrive())
                .alreadyExists(false)
                .needsMigration(false)
                .build();
    }
    
    /**
     * 为新月份选择目标盘（简化版本，优先E盘）
     * 
     * @return 目标选择结果
     */
    private PreSyncService.TargetSelection selectTargetForNewMonth() {
        // 检查E盘是否可用
        if (Files.exists(config.getTargetEPath())) {
            long eFreeSizeGB = fileSystemUtils.getDiskFreeSpaceGB(config.getTargetEPath());
            if (eFreeSizeGB > config.getTargetEMinFreeGb()) {
                log.info("选择E盘作为目标，可用空间: {}GB", eFreeSizeGB);
                return PreSyncService.TargetSelection.builder()
                        .targetPath(config.getTargetEPath())
                        .drive("E:")
                        .freeSpaceGB(eFreeSizeGB)
                        .build();
            }
        }
        
        // 检查F盘是否可用
        if (Files.exists(config.getTargetFPath())) {
            long fFreeSizeGB = fileSystemUtils.getDiskFreeSpaceGB(config.getTargetFPath());
            if (fFreeSizeGB > config.getTargetFMinFreeGb()) {
                log.info("选择F盘作为目标，可用空间: {}GB", fFreeSizeGB);
                return PreSyncService.TargetSelection.builder()
                        .targetPath(config.getTargetFPath())
                        .drive("F:")
                        .freeSpaceGB(fFreeSizeGB)
                        .build();
            }
        }
        
        log.error("没有可用的目标盘");
        return PreSyncService.TargetSelection.builder().build();
    }
    
    /**
     * 检查指定月份的联接状态
     * 
     * @param month 月份
     * @return 联接状态
     */
    public MonthLinkStatus checkMonthLinkStatus(String month) {
        Path sourceDir = config.getSourceRootPath().resolve(month);
        
        if (!Files.exists(sourceDir)) {
            return MonthLinkStatus.builder()
                    .month(month)
                    .exists(false)
                    .isJunction(false)
                    .status("不存在")
                    .build();
        }
        
        boolean isJunction = fileSystemUtils.isJunction(sourceDir);
        String status;
        String targetPath = null;
        
        if (isJunction) {
            status = "目录联接";
            // 尝试找到目标路径
            Path eTarget = config.getTargetEPath().resolve(month);
            Path fTarget = config.getTargetFPath().resolve(month);
            
            if (Files.exists(eTarget)) {
                targetPath = eTarget.toString();
            } else if (Files.exists(fTarget)) {
                targetPath = fTarget.toString();
            }
        } else {
            status = "实体目录";
        }
        
        return MonthLinkStatus.builder()
                .month(month)
                .exists(true)
                .isJunction(isJunction)
                .status(status)
                .sourceDir(sourceDir.toString())
                .targetDir(targetPath)
                .build();
    }
    
    /**
     * 计划任务结果
     */
    @lombok.Builder
    @lombok.Data
    public static class ScheduleTaskResult {
        private boolean success;
        private String message;
        private String taskName;
        private String schedule;
        private String scriptPath;
        
        /**
         * 获取结果摘要
         */
        public String getSummary() {
            return String.format(
                "计划任务创建结果: %s\n" +
                "任务名称: %s\n" +
                "执行时间: %s\n" +
                "执行脚本: %s\n" +
                "详细信息: %s",
                success ? "成功" : "失败",
                taskName, schedule, scriptPath, message
            );
        }
    }
    
    /**
     * 月份联接结果
     */
    @lombok.Builder
    @lombok.Data
    public static class MonthLinkResult {
        private boolean success;
        private String message;
        private String month;
        private String sourceDir;
        private String targetDir;
        private String targetDrive;
        private boolean alreadyExists;
        private boolean needsMigration;
        
        /**
         * 获取结果摘要
         */
        public String getSummary() {
            return String.format(
                "月份联接结果 - 月份: %s, 结果: %s\n" +
                "源目录: %s\n" +
                "目标目录: %s (%s)\n" +
                "状态: %s\n" +
                "详细信息: %s",
                month, success ? "成功" : "失败",
                sourceDir != null ? sourceDir : "未设置",
                targetDir != null ? targetDir : "未设置",
                targetDrive != null ? targetDrive : "未知",
                alreadyExists ? "已存在" : (needsMigration ? "需要迁移" : "新建"),
                message
            );
        }
    }
    
    /**
     * 月份联接状态
     */
    @lombok.Builder
    @lombok.Data
    public static class MonthLinkStatus {
        private String month;
        private boolean exists;
        private boolean isJunction;
        private String status;
        private String sourceDir;
        private String targetDir;
        
        /**
         * 获取状态摘要
         */
        public String getSummary() {
            return String.format(
                "月份联接状态 - 月份: %s\n" +
                "存在: %s\n" +
                "类型: %s\n" +
                "源目录: %s\n" +
                "目标目录: %s",
                month, exists ? "是" : "否", status,
                sourceDir != null ? sourceDir : "不存在",
                targetDir != null ? targetDir : "未知"
            );
        }
    }
}
