package com.cirpoint.datapkg.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 数据包迁移配置类
 * 对应原脚本：01-config.bat
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@Component
@ConfigurationProperties(prefix = "datapkg.migrate")
public class DataPkgMigrateConfig {
    
    /**
     * 源目录路径
     */
    private String sourceRoot = "D:\\DataPkgFile";
    
    /**
     * E盘目标目录
     */
    private String targetE = "E:\\DataPkgFile";
    
    /**
     * F盘目标目录
     */
    private String targetF = "F:\\DataPkgFile";
    
    /**
     * 日志目录
     */
    private String logDir = "D:\\eclipse-work\\DataPkg-Migrate\\logs";
    
    /**
     * 服务名称（可选）
     */
    private String serviceName = "";
    
    /**
     * 分配策略：FILL_E_THEN_F 或 BALANCE_BY_MONTH
     */
    private String strategy = "FILL_E_THEN_F";
    
    /**
     * E盘预留容量（GB）
     */
    private int targetEMinFreeGb = 50;
    
    /**
     * F盘预留容量（GB）
     */
    private int targetFMinFreeGb = 50;
    
    /**
     * 预同步排除最近N个月
     */
    private int excludeRecentMonths = 2;
    
    /**
     * 多线程数量
     */
    private int threads = 16;
    
    /**
     * Robocopy重试次数
     */
    private int roboRetry = 1;
    
    /**
     * Robocopy等待时间（秒）
     */
    private int roboWait = 2;
    
    /**
     * 安全开关：true=演练（只打印命令），false=执行
     */
    private boolean dryRun = false;
    
    /**
     * 切换后是否删除_old：true=删除，false=保留
     */
    private boolean deleteOld = true;
    
    /**
     * 抽样验证文件数量（每月抽N个文件哈希校验，可设0跳过）
     */
    private int verifySampleCount = 5;
    
    /**
     * 分配策略枚举
     */
    public enum Strategy {
        FILL_E_THEN_F,
        BALANCE_BY_MONTH
    }
    
    /**
     * 获取分配策略枚举
     */
    public Strategy getStrategyEnum() {
        try {
            return Strategy.valueOf(strategy.toUpperCase());
        } catch (IllegalArgumentException e) {
            return Strategy.FILL_E_THEN_F;
        }
    }
    
    /**
     * 获取源目录Path对象
     */
    public Path getSourceRootPath() {
        return Paths.get(sourceRoot);
    }
    
    /**
     * 获取E盘目标目录Path对象
     */
    public Path getTargetEPath() {
        return Paths.get(targetE);
    }
    
    /**
     * 获取F盘目标目录Path对象
     */
    public Path getTargetFPath() {
        return Paths.get(targetF);
    }
    
    /**
     * 获取日志目录Path对象
     */
    public Path getLogDirPath() {
        return Paths.get(logDir);
    }
    
    /**
     * 初始化日志目录
     */
    public void initLogDir() {
        File logDirFile = new File(logDir);
        if (!logDirFile.exists()) {
            logDirFile.mkdirs();
        }
    }
    
    /**
     * 验证配置有效性
     */
    public void validate() {
        if (sourceRoot == null || sourceRoot.trim().isEmpty()) {
            throw new IllegalArgumentException("源目录不能为空");
        }
        
        if (targetE == null || targetE.trim().isEmpty()) {
            throw new IllegalArgumentException("E盘目标目录不能为空");
        }
        
        if (targetF == null || targetF.trim().isEmpty()) {
            throw new IllegalArgumentException("F盘目标目录不能为空");
        }
        
        if (logDir == null || logDir.trim().isEmpty()) {
            throw new IllegalArgumentException("日志目录不能为空");
        }
        
        if (targetEMinFreeGb < 0) {
            throw new IllegalArgumentException("E盘预留容量不能为负数");
        }
        
        if (targetFMinFreeGb < 0) {
            throw new IllegalArgumentException("F盘预留容量不能为负数");
        }
        
        if (excludeRecentMonths < 0) {
            throw new IllegalArgumentException("排除最近月份数不能为负数");
        }
        
        if (threads <= 0) {
            throw new IllegalArgumentException("线程数必须大于0");
        }
        
        if (roboRetry < 0) {
            throw new IllegalArgumentException("重试次数不能为负数");
        }
        
        if (roboWait < 0) {
            throw new IllegalArgumentException("等待时间不能为负数");
        }
        
        if (verifySampleCount < 0) {
            throw new IllegalArgumentException("抽样验证数量不能为负数");
        }
    }
    
    /**
     * 获取配置摘要信息
     */
    public String getSummary() {
        return String.format(
            "数据包迁移配置:\n" +
            "  源目录: %s\n" +
            "  E盘目标: %s (预留%dGB)\n" +
            "  F盘目标: %s (预留%dGB)\n" +
            "  分配策略: %s\n" +
            "  排除最近: %d个月\n" +
            "  线程数: %d\n" +
            "  演练模式: %s\n" +
            "  删除旧目录: %s\n" +
            "  抽样验证: %d个文件",
            sourceRoot, targetE, targetEMinFreeGb, targetF, targetFMinFreeGb,
            strategy, excludeRecentMonths, threads, 
            dryRun ? "是" : "否", deleteOld ? "是" : "否", verifySampleCount
        );
    }
}
