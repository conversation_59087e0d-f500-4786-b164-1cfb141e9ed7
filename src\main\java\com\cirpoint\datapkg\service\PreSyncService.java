package com.cirpoint.datapkg.service;

import com.cirpoint.datapkg.config.DataPkgMigrateConfig;
import com.cirpoint.datapkg.util.FileSystemUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * 预同步服务
 * 对应原脚本：20-pre-sync.bat
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PreSyncService {
    
    private final DataPkgMigrateConfig config;
    private final FileSystemUtils fileSystemUtils;
    private final MonthListService monthListService;
    
    /**
     * 执行预同步
     * 
     * @return 预同步结果
     */
    public PreSyncResult executePreSync() {
        log.info("开始执行预同步");
        
        // 检查待迁移月份清单文件
        if (!monthListService.checkMonthListFiles()) {
            return PreSyncResult.builder()
                    .success(false)
                    .message("未找到月份清单文件，请先运行月份清单生成")
                    .build();
        }
        
        // 读取待迁移月份列表
        List<String> monthsToMigrate = monthListService.readMigrateMonthsList();
        if (monthsToMigrate.isEmpty()) {
            return PreSyncResult.builder()
                    .success(false)
                    .message("待迁移月份列表为空")
                    .build();
        }
        
        log.info("待预同步月份数: {}", monthsToMigrate.size());
        
        List<MonthSyncResult> results = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;
        
        for (String month : monthsToMigrate) {
            MonthSyncResult result = preSyncMonth(month);
            results.add(result);
            
            if (result.isSuccess()) {
                successCount++;
            } else {
                failCount++;
            }
        }
        
        boolean overallSuccess = failCount == 0;
        String message = String.format("预同步完成 - 成功: %d, 失败: %d", successCount, failCount);
        
        log.info(message);
        
        return PreSyncResult.builder()
                .success(overallSuccess)
                .message(message)
                .totalMonths(monthsToMigrate.size())
                .successCount(successCount)
                .failCount(failCount)
                .monthResults(results)
                .build();
    }
    
    /**
     * 预同步单个月份
     * 
     * @param month 月份
     * @return 同步结果
     */
    private MonthSyncResult preSyncMonth(String month) {
        log.info("预同步月份: {}", month);
        
        Path sourceDir = config.getSourceRootPath().resolve(month);
        if (!Files.exists(sourceDir)) {
            log.warn("源目录不存在，跳过: {}", sourceDir);
            return MonthSyncResult.builder()
                    .month(month)
                    .success(false)
                    .message("源目录不存在")
                    .build();
        }
        
        // 选择目标盘
        TargetSelection targetSelection = selectTarget(month);
        if (targetSelection.getTargetPath() == null) {
            log.error("未找到可用目标盘，跳过月份: {}", month);
            return MonthSyncResult.builder()
                    .month(month)
                    .success(false)
                    .message("未找到可用目标盘")
                    .build();
        }
        
        Path targetDir = targetSelection.getTargetPath().resolve(month);
        
        // 构建Robocopy选项
        String options = String.format(
                "/E /COPY:DATSO /DCOPY:DAT /R:%d /W:%d /MT:%d /FFT /XJ /TEE",
                config.getRoboRetry(), config.getRoboWait(), config.getThreads()
        );
        
        // 日志文件
        Path logFile = config.getLogDirPath().resolve("pre_" + month + ".log");
        
        // 执行Robocopy
        boolean success = fileSystemUtils.robocopy(sourceDir, targetDir, options, logFile, config.isDryRun());
        
        String message = success ? "预同步成功" : "预同步失败";
        log.info("月份 {} 预同步结果: {}", month, message);
        
        return MonthSyncResult.builder()
                .month(month)
                .success(success)
                .message(message)
                .sourceDir(sourceDir.toString())
                .targetDir(targetDir.toString())
                .targetDrive(targetSelection.getDrive())
                .logFile(logFile.toString())
                .build();
    }
    
    /**
     * 选择目标盘
     * 
     * @param month 月份
     * @return 目标选择结果
     */
    public TargetSelection selectTarget(String month) {
        long eFreeSizeGB = fileSystemUtils.getDiskFreeSpaceGB(config.getTargetEPath());
        long fFreeSizeGB = fileSystemUtils.getDiskFreeSpaceGB(config.getTargetFPath());
        
        log.debug("磁盘空间检查 - E盘: {}GB, F盘: {}GB", eFreeSizeGB, fFreeSizeGB);
        
        DataPkgMigrateConfig.Strategy strategy = config.getStrategyEnum();
        
        if (strategy == DataPkgMigrateConfig.Strategy.BALANCE_BY_MONTH) {
            return selectTargetByBalance(month, eFreeSizeGB, fFreeSizeGB);
        } else {
            return selectTargetByFillE(eFreeSizeGB, fFreeSizeGB);
        }
    }
    
    /**
     * 按奇偶月均衡分配策略选择目标盘
     */
    private TargetSelection selectTargetByBalance(String month, long eFreeSizeGB, long fFreeSizeGB) {
        // 提取月份数字
        String[] parts = month.split("-");
        if (parts.length != 2) {
            log.error("月份格式错误: {}", month);
            return TargetSelection.builder().build();
        }
        
        try {
            int monthNum = Integer.parseInt(parts[1]);
            boolean isOddMonth = (monthNum % 2) == 1;
            
            if (isOddMonth) {
                // 奇数月优先E盘
                if (eFreeSizeGB > config.getTargetEMinFreeGb()) {
                    return TargetSelection.builder()
                            .targetPath(config.getTargetEPath())
                            .drive("E:")
                            .freeSpaceGB(eFreeSizeGB)
                            .build();
                } else if (fFreeSizeGB > config.getTargetFMinFreeGb()) {
                    return TargetSelection.builder()
                            .targetPath(config.getTargetFPath())
                            .drive("F:")
                            .freeSpaceGB(fFreeSizeGB)
                            .build();
                }
            } else {
                // 偶数月优先F盘
                if (fFreeSizeGB > config.getTargetFMinFreeGb()) {
                    return TargetSelection.builder()
                            .targetPath(config.getTargetFPath())
                            .drive("F:")
                            .freeSpaceGB(fFreeSizeGB)
                            .build();
                } else if (eFreeSizeGB > config.getTargetEMinFreeGb()) {
                    return TargetSelection.builder()
                            .targetPath(config.getTargetEPath())
                            .drive("E:")
                            .freeSpaceGB(eFreeSizeGB)
                            .build();
                }
            }
        } catch (NumberFormatException e) {
            log.error("解析月份数字失败: {}", month, e);
        }
        
        return TargetSelection.builder().build();
    }
    
    /**
     * 按优先填满E盘策略选择目标盘
     */
    private TargetSelection selectTargetByFillE(long eFreeSizeGB, long fFreeSizeGB) {
        if (eFreeSizeGB > config.getTargetEMinFreeGb()) {
            return TargetSelection.builder()
                    .targetPath(config.getTargetEPath())
                    .drive("E:")
                    .freeSpaceGB(eFreeSizeGB)
                    .build();
        } else if (fFreeSizeGB > config.getTargetFMinFreeGb()) {
            return TargetSelection.builder()
                    .targetPath(config.getTargetFPath())
                    .drive("F:")
                    .freeSpaceGB(fFreeSizeGB)
                    .build();
        }
        
        return TargetSelection.builder().build();
    }
    
    /**
     * 目标盘选择结果
     */
    @lombok.Builder
    @lombok.Data
    public static class TargetSelection {
        private Path targetPath;
        private String drive;
        private long freeSpaceGB;
    }
    
    /**
     * 月份同步结果
     */
    @lombok.Builder
    @lombok.Data
    public static class MonthSyncResult {
        private String month;
        private boolean success;
        private String message;
        private String sourceDir;
        private String targetDir;
        private String targetDrive;
        private String logFile;
    }
    
    /**
     * 预同步结果
     */
    @lombok.Builder
    @lombok.Data
    public static class PreSyncResult {
        private boolean success;
        private String message;
        private int totalMonths;
        private int successCount;
        private int failCount;
        private List<MonthSyncResult> monthResults;
        
        /**
         * 获取结果摘要
         */
        public String getSummary() {
            if (!success) {
                return "失败: " + message;
            }
            
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("预同步完成 - 总计: %d, 成功: %d, 失败: %d\n", 
                    totalMonths, successCount, failCount));
            
            if (monthResults != null) {
                for (MonthSyncResult result : monthResults) {
                    sb.append(String.format("  %s: %s -> %s (%s)\n", 
                            result.getMonth(), 
                            result.isSuccess() ? "成功" : "失败",
                            result.getTargetDrive() != null ? result.getTargetDrive() : "无",
                            result.getMessage()));
                }
            }
            
            return sb.toString();
        }
    }
}
