package com.cirpoint.datapkg.controller;

import com.cirpoint.datapkg.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 数据包迁移命令行控制器
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataPkgMigrateController implements CommandLineRunner {
    
    private final ApplicationContext applicationContext;
    private final DataPkgMigrateService dataPkgMigrateService;
    private final MonthListService monthListService;
    private final PreSyncService preSyncService;
    private final CutoverService cutoverService;
    private final RollbackService rollbackService;
    private final ScheduleService scheduleService;
    
    @Override
    public void run(String... args) throws Exception {
        if (args.length == 0) {
            printUsage();
            return;
        }
        
        String command = args[0];
        
        try {
            switch (command.toLowerCase()) {
                case "status":
                case "check":
                    handleStatus();
                    break;
                    
                case "list-months":
                    handleListMonths();
                    break;
                    
                case "pre-sync":
                    handlePreSync();
                    break;
                    
                case "cutover":
                    handleCutover();
                    break;
                    
                case "rollback":
                    if (args.length < 2) {
                        System.err.println("错误: rollback 命令需要指定月份参数");
                        System.err.println("用法: java -jar file-handle.jar rollback YYYY-MM");
                        System.exit(1);
                    }
                    handleRollback(args[1]);
                    break;
                    
                case "rollback-check":
                    if (args.length < 2) {
                        System.err.println("错误: rollback-check 命令需要指定月份参数");
                        System.err.println("用法: java -jar file-handle.jar rollback-check YYYY-MM");
                        System.exit(1);
                    }
                    handleRollbackCheck(args[1]);
                    break;
                    
                case "create-schedule":
                    handleCreateSchedule();
                    break;
                    
                case "create-current-month-link":
                    handleCreateCurrentMonthLink();
                    break;
                    
                case "create-month-link":
                    if (args.length < 2) {
                        System.err.println("错误: create-month-link 命令需要指定月份参数");
                        System.err.println("用法: java -jar file-handle.jar create-month-link YYYY-MM");
                        System.exit(1);
                    }
                    handleCreateMonthLink(args[1]);
                    break;
                    
                case "check-month-link":
                    if (args.length < 2) {
                        System.err.println("错误: check-month-link 命令需要指定月份参数");
                        System.err.println("用法: java -jar file-handle.jar check-month-link YYYY-MM");
                        System.exit(1);
                    }
                    handleCheckMonthLink(args[1]);
                    break;
                    
                case "run-all":
                    handleRunAll();
                    break;
                    
                case "help":
                case "--help":
                case "-h":
                    printUsage();
                    break;
                    
                default:
                    System.err.println("未知命令: " + command);
                    printUsage();
                    System.exit(1);
            }
        } catch (Exception e) {
            log.error("执行命令失败: {}", command, e);
            System.err.println("执行命令失败: " + e.getMessage());
            System.exit(1);
        }
        
        // 正常退出
        SpringApplication.exit(applicationContext, () -> 0);
    }
    
    private void handleStatus() {
        System.out.println("检查系统状态...");
        DataPkgMigrateService.SystemStatusResult result = dataPkgMigrateService.checkSystemStatus();
        System.out.println(result.getMessage());
        
        if (!result.isSuccess()) {
            System.exit(1);
        }
    }
    
    private void handleListMonths() {
        System.out.println("生成月份清单...");
        MonthListService.MonthListResult result = monthListService.generateMonthList();
        System.out.println(result.getSummary());
        
        if (!result.isSuccess()) {
            System.exit(1);
        }
    }
    
    private void handlePreSync() {
        System.out.println("执行预同步...");
        PreSyncService.PreSyncResult result = preSyncService.executePreSync();
        System.out.println(result.getSummary());
        
        if (!result.isSuccess()) {
            System.exit(1);
        }
    }
    
    private void handleCutover() {
        System.out.println("执行切换...");
        CutoverService.CutoverResult result = cutoverService.executeCutover();
        System.out.println(result.getSummary());
        
        if (!result.isSuccess()) {
            System.exit(1);
        }
    }
    
    private void handleRollback(String month) {
        System.out.println("执行回滚: " + month);
        RollbackService.RollbackResult result = rollbackService.rollbackMonth(month);
        System.out.println(result.getSummary());
        
        if (!result.isSuccess()) {
            System.exit(1);
        }
    }
    
    private void handleRollbackCheck(String month) {
        System.out.println("检查回滚可用性: " + month);
        RollbackService.RollbackCheckResult result = rollbackService.checkRollbackAvailable(month);
        System.out.println(result.getSummary());
        
        if (!result.isCanRollback()) {
            System.exit(1);
        }
    }
    
    private void handleCreateSchedule() {
        System.out.println("创建自动联接计划任务...");
        ScheduleService.ScheduleTaskResult result = scheduleService.createAutoLinkSchedule();
        System.out.println(result.getSummary());
        
        if (!result.isSuccess()) {
            System.exit(1);
        }
    }
    
    private void handleCreateCurrentMonthLink() {
        System.out.println("创建当前月份联接...");
        ScheduleService.MonthLinkResult result = scheduleService.createCurrentMonthLink();
        System.out.println(result.getSummary());
        
        if (!result.isSuccess()) {
            System.exit(1);
        }
    }
    
    private void handleCreateMonthLink(String month) {
        System.out.println("创建月份联接: " + month);
        ScheduleService.MonthLinkResult result = scheduleService.createMonthLink(month);
        System.out.println(result.getSummary());
        
        if (!result.isSuccess()) {
            System.exit(1);
        }
    }
    
    private void handleCheckMonthLink(String month) {
        System.out.println("检查月份联接状态: " + month);
        ScheduleService.MonthLinkStatus result = scheduleService.checkMonthLinkStatus(month);
        System.out.println(result.getSummary());
    }
    
    private void handleRunAll() {
        System.out.println("执行完整迁移流程...");
        DataPkgMigrateService.MigrateAllResult result = dataPkgMigrateService.runAll();
        System.out.println(result.getSummary());
        
        if (!result.isSuccess()) {
            System.exit(1);
        }
    }
    
    private void printUsage() {
        System.out.println("数据包迁移工具 - Java版本");
        System.out.println("作者: wanghq");
        System.out.println("日期: 2025-08-27");
        System.out.println();
        System.out.println("用法: java -jar file-handle.jar [命令] [参数]");
        System.out.println();
        System.out.println("可用命令:");
        System.out.println("  status                    - 检查系统状态");
        System.out.println("  list-months              - 生成月份清单");
        System.out.println("  pre-sync                 - 执行预同步");
        System.out.println("  cutover                  - 执行切换");
        System.out.println("  rollback YYYY-MM         - 回滚指定月份");
        System.out.println("  rollback-check YYYY-MM   - 检查月份回滚可用性");
        System.out.println("  create-schedule          - 创建自动联接计划任务");
        System.out.println("  create-current-month-link - 创建当前月份联接");
        System.out.println("  create-month-link YYYY-MM - 创建指定月份联接");
        System.out.println("  check-month-link YYYY-MM  - 检查月份联接状态");
        System.out.println("  run-all                  - 执行完整迁移流程");
        System.out.println("  help                     - 显示此帮助信息");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  java -jar file-handle.jar status");
        System.out.println("  java -jar file-handle.jar run-all");
        System.out.println("  java -jar file-handle.jar rollback 2024-01");
        System.out.println("  java -jar file-handle.jar create-month-link 2024-12");
        System.out.println();
        System.out.println("配置文件: application-datapkg.yml");
        System.out.println("使用 --spring.profiles.active=datapkg 激活配置");
    }
}
