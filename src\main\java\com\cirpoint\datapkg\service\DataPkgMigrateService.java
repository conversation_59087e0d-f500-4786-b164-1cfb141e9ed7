package com.cirpoint.datapkg.service;

import com.cirpoint.datapkg.config.DataPkgMigrateConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 数据包迁移主服务
 * 对应原脚本：60-run-all.bat
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataPkgMigrateService {
    
    private final DataPkgMigrateConfig config;
    private final MonthListService monthListService;
    private final PreSyncService preSyncService;
    private final CutoverService cutoverService;
    private final RollbackService rollbackService;
    private final ScheduleService scheduleService;
    
    /**
     * 执行完整的迁移流程（对应60-run-all.bat）
     * 
     * @return 执行结果
     */
    public MigrateAllResult runAll() {
        log.info("开始执行完整的数据包迁移流程");
        log.info("配置摘要:\n{}", config.getSummary());
        
        MigrateAllResult.MigrateAllResultBuilder resultBuilder = MigrateAllResult.builder();
        
        // 第1步：生成月份清单
        log.info("========= 第 1 步：生成月份清单 =========");
        MonthListService.MonthListResult monthListResult;
        
        if (monthListService.checkMonthListFiles()) {
            log.info("发现已存在月份清单文件，跳过生成步骤");
            monthListResult = MonthListService.MonthListResult.builder()
                    .success(true)
                    .message("使用已存在的月份清单文件")
                    .build();
        } else {
            monthListResult = monthListService.generateMonthList();
        }
        
        resultBuilder.monthListResult(monthListResult);
        
        if (!monthListResult.isSuccess()) {
            log.error("生成月份清单失败: {}", monthListResult.getMessage());
            return resultBuilder
                    .success(false)
                    .message("生成月份清单失败")
                    .build();
        }
        
        log.info("月份清单生成完成");
        
        // 第2步：预同步
        log.info("========= 第 2 步：预同步（Pre-sync） =========");
        PreSyncService.PreSyncResult preSyncResult = preSyncService.executePreSync();
        resultBuilder.preSyncResult(preSyncResult);
        
        if (!preSyncResult.isSuccess()) {
            log.error("预同步失败: {}", preSyncResult.getMessage());
            return resultBuilder
                    .success(false)
                    .message("预同步失败")
                    .build();
        }
        
        log.info("预同步完成");
        
        // 第3步：执行切换
        log.info("========= 第 3 步：执行切换 =========");
        CutoverService.CutoverResult cutoverResult = cutoverService.executeCutover();
        resultBuilder.cutoverResult(cutoverResult);
        
        if (!cutoverResult.isSuccess()) {
            log.error("切换失败: {}", cutoverResult.getMessage());
            return resultBuilder
                    .success(false)
                    .message("切换失败")
                    .build();
        }
        
        log.info("切换完成");
        
        // 完成
        log.info("========= 完成 =========");
        log.info("- 请检查应用功能、日志与磁盘空间");
        log.info("- 稳定运行后，可将 DELETE_OLD=true，并再次运行切换脚本以清理旧目录");
        
        return resultBuilder
                .success(true)
                .message("数据包迁移流程执行成功")
                .build();
    }
    
    /**
     * 检查系统状态
     * 
     * @return 状态检查结果
     */
    public SystemStatusResult checkSystemStatus() {
        log.info("检查系统状态");
        
        // 检查配置
        try {
            config.validate();
            config.initLogDir();
        } catch (Exception e) {
            return SystemStatusResult.builder()
                    .success(false)
                    .message("配置验证失败: " + e.getMessage())
                    .build();
        }
        
        // 检查源目录
        boolean sourceExists = config.getSourceRootPath().toFile().exists();
        
        // 检查目标目录
        boolean targetEExists = config.getTargetEPath().toFile().exists();
        boolean targetFExists = config.getTargetFPath().toFile().exists();
        
        // 检查磁盘空间
        long eFreeSizeGB = targetEExists ? 
                fileSystemUtils.getDiskFreeSpaceGB(config.getTargetEPath()) : 0;
        long fFreeSizeGB = targetFExists ? 
                fileSystemUtils.getDiskFreeSpaceGB(config.getTargetFPath()) : 0;
        
        // 检查月份清单文件
        boolean monthListExists = monthListService.checkMonthListFiles();
        
        StringBuilder statusMsg = new StringBuilder();
        statusMsg.append("系统状态检查:\n");
        statusMsg.append(String.format("  源目录: %s (%s)\n", 
                config.getSourceRoot(), sourceExists ? "存在" : "不存在"));
        statusMsg.append(String.format("  E盘目标: %s (%s, %dGB可用)\n", 
                config.getTargetE(), targetEExists ? "存在" : "不存在", eFreeSizeGB));
        statusMsg.append(String.format("  F盘目标: %s (%s, %dGB可用)\n", 
                config.getTargetF(), targetFExists ? "存在" : "不存在", fFreeSizeGB));
        statusMsg.append(String.format("  日志目录: %s\n", config.getLogDir()));
        statusMsg.append(String.format("  月份清单: %s\n", monthListExists ? "存在" : "不存在"));
        statusMsg.append(String.format("  演练模式: %s\n", config.isDryRun() ? "开启" : "关闭"));
        
        boolean systemReady = sourceExists && (targetEExists || targetFExists);
        
        return SystemStatusResult.builder()
                .success(systemReady)
                .message(statusMsg.toString())
                .sourceExists(sourceExists)
                .targetEExists(targetEExists)
                .targetFExists(targetFExists)
                .eFreeSizeGB(eFreeSizeGB)
                .fFreeSizeGB(fFreeSizeGB)
                .monthListExists(monthListExists)
                .build();
    }
    
    private final FileSystemUtils fileSystemUtils;
    
    /**
     * 完整迁移结果
     */
    @lombok.Builder
    @lombok.Data
    public static class MigrateAllResult {
        private boolean success;
        private String message;
        private MonthListService.MonthListResult monthListResult;
        private PreSyncService.PreSyncResult preSyncResult;
        private CutoverService.CutoverResult cutoverResult;
        
        /**
         * 获取结果摘要
         */
        public String getSummary() {
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("数据包迁移流程结果: %s\n", success ? "成功" : "失败"));
            sb.append(String.format("总体信息: %s\n\n", message));
            
            if (monthListResult != null) {
                sb.append("=== 月份清单生成 ===\n");
                sb.append(monthListResult.getSummary()).append("\n\n");
            }
            
            if (preSyncResult != null) {
                sb.append("=== 预同步 ===\n");
                sb.append(preSyncResult.getSummary()).append("\n\n");
            }
            
            if (cutoverResult != null) {
                sb.append("=== 切换 ===\n");
                sb.append(cutoverResult.getSummary()).append("\n");
            }
            
            return sb.toString();
        }
    }
    
    /**
     * 系统状态检查结果
     */
    @lombok.Builder
    @lombok.Data
    public static class SystemStatusResult {
        private boolean success;
        private String message;
        private boolean sourceExists;
        private boolean targetEExists;
        private boolean targetFExists;
        private long eFreeSizeGB;
        private long fFreeSizeGB;
        private boolean monthListExists;
    }
}
