@echo off
chcp 65001 >nul 2>nul
setlocal ENABLEDELAYEDEXPANSION
call "%~dp0\01-config.bat"

REM 获取下个月份
for /f "delims=" %%d in ('powershell -NoProfile -Command "(Get-Date).AddMonths(1).ToString('yyyy-MM')"') do set MM=%%d

echo 下个月份：%MM%

REM 简化的目标选择逻辑
set "TARGET="
if exist "E:\DataPkgFile" (
  set "TARGET=%TARGET_E%"
  echo 检测到E盘，设置为目标
)
if "!TARGET!"=="" if exist "F:\DataPkgFile" (
  set "TARGET=%TARGET_F%"
  echo 检测到F盘，设置为目标
)

if "!TARGET!"=="" (
  echo 错误：无可用目标盘
  exit /b 1
)

echo 选择目标：!TARGET!

if not exist "!TARGET!\%MM%" (
  echo 创建目标目录：!TARGET!\%MM%
  mkdir "!TARGET!\%MM%"
)

REM 检查源目录状态
if exist "%SOURCE_ROOT%\%MM%" (
  REM 检查是否已经是链接
  dir "%SOURCE_ROOT%\%MM%" | find "<JUNCTION>" >nul
  if !errorlevel! equ 0 (
    echo 链接已存在：%SOURCE_ROOT%\%MM% -^> !TARGET!\%MM%
    exit /b 0
  ) else (
    echo 发现实体目录：%SOURCE_ROOT%\%MM%，需要先迁移数据
    echo 建议运行 30-cutover-month.bat %MM% 进行迁移
    exit /b 1
  )
)

echo 创建链接：%SOURCE_ROOT%\%MM% -^> !TARGET!\%MM%
mklink /J "%SOURCE_ROOT%\%MM%" "!TARGET!\%MM%"
if !errorlevel! equ 0 (
  echo 链接创建成功
) else (
  echo 错误：链接创建失败
  exit /b 1
)
exit /b 0
