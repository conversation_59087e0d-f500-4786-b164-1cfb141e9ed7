# 按月目录联接到 E/F——基于现有脚本的执行步骤

适用环境：Windows Server 2008 R2（离线）、项目目录 `D:\DataPkgFile`，按月目录结构（`yyyy-MM`）。目标是在不改业务代码和备份路径的前提下，将历史月份迁移到 E/F 大盘，并在 `D:\DataPkgFile\yyyy-MM` 上创建 NTFS 目录联接（Junction），从而释放 D 盘空间。

**重要说明：**
- 全流程默认不修改备份任务（仍从 `D:\DataPkgFile` 备份到 `G:\DataPkgFile`）。请确认备份使用 Robocopy 时未启用 `/SL` 参数，以确保实际内容被备份。
- 所有脚本均需"以管理员身份运行"。
- 建议先选取一个较小月份试点，验证通过后再批量推进。

---

## 0. 目录规划与术语

- **源目录**（应用读写不变）：`D:\DataPkgFile`
- **目标挂载目录**：
  - 优先：`E:\DataPkgFile`
  - 其次：`F:\DataPkgFile`
- **月份目录示例**：`D:\DataPkgFile\2022-07`
- **迁移后**：`D:\DataPkgFile\2022-07` 将被替换为指向 `E:\DataPkgFile\2022-07` 或 `F:\DataPkgFile\2022-07` 的"目录联接（Junction）"。

---

## 1. 脚本概览（基于现有实现）

现有脚本框架包含以下文件：

### 核心脚本
- **`01-config.bat`**：全局配置（源目录、目标目录、策略、安全开关等）
- **`10-list-months.bat`**：生成月份清单（兼容 PowerShell 与 CMD 双方案）
- **`20-pre-sync.bat`**：不停机"预同步"（多次可重复执行）
- **`30-cutover-month.bat`**：维护窗口内"最终差异同步 + 切换为联接 + 验证"
- **`40-rollback-month.bat`**：遇故障的回滚脚本
- **`50-autolink-schedule.bat`**：创建"每月首日自动联接"的计划任务
- **`60-run-all.bat`**：一键编排（从"列月→预同步→切换"）

### 辅助脚本
- **`create_current_month_link.bat`**：创建下个月份的目录联接

### 脚本特点
- **兼容性**：兼容 Windows Server 2008 R2，默认依赖 CMD；如有 PowerShell（v2+），将自动利用增强能力
- **安全开关**：`DRY_RUN` 安全开关默认开启（只打印将执行的命令，不落地变更）
- **智能分配**：具备"保留磁盘余量阈值"的判断，支持两种分配策略：
  - `FILL_E_THEN_F`：优先填满 E 盘后再用 F 盘
  - `BALANCE_BY_MONTH`：奇偶月均衡分配
- **多线程**：使用 16 线程并发复制，提高效率
- **验证机制**：支持抽样哈希校验和 robocopy 差异验证

---

## 2. 快速开始（基于现有脚本）

### 步骤 1：环境准备
1. 以管理员身份打开 CMD
2. 确认目录结构：
   ```
   D:\DataPkg-Migrate\
   ├── bin\          # 脚本目录
   └── logs\         # 日志目录
   ```

### 步骤 2：配置调整
编辑 `01-config.bat`，根据实际情况调整：

```batch
REM 源与目标目录
set "SOURCE_ROOT=D:\DataPkgFile"
set "TARGET_E=E:\DataPkgFile"
set "TARGET_F=F:\DataPkgFile"

REM 服务名（可选）：如 Tomcat8.5；若无则留空
set "SERVICE_NAME="

REM 分配策略：FILL_E_THEN_F 或 BALANCE_BY_MONTH
set "STRATEGY=BALANCE_BY_MONTH"

REM 预留容量（GB），低于该值停止向该盘写入
set "TARGET_E_MIN_FREE_GB=50"
set "TARGET_F_MIN_FREE_GB=50"

REM 预同步排除最近 N 个月
set "EXCLUDE_RECENT_MONTHS=2"

REM 安全开关：1=演练（只打印命令），0=执行
set "DRY_RUN=1"

REM 切换后是否删除 _old：1=删除，0=保留
set "DELETE_OLD=0"
```

### 步骤 3：生成月份清单
```batch
cd /d D:\DataPkg-Migrate\bin
10-list-months.bat
```

**输出文件：**
- `months.txt`：所有 yyyy-MM 目录列表
- `months_to_migrate.txt`：排除最近 2 个月后的迁移清单

**注意：** 如果 PowerShell 不可用，需要人工编辑 `months_to_migrate.txt`，从 `months.txt` 复制需要迁移的月份。

### 步骤 4：预同步（可多次执行）
```batch
20-pre-sync.bat
```

**特点：**
- 使用 `/E` 参数，不删除目标多余文件
- 可在业务运行期多次累积执行
- 日志保存在 `D:\DataPkg-Migrate\logs\pre_*.log`

### 步骤 5：维护窗口切换
1. 通知业务停机窗口，确认可停应用写入
2. 将 `DRY_RUN=0`（正式执行）
3. 执行切换：
   ```batch
   30-cutover-month.bat
   ```

**切换过程：**
- 停止服务（若配置了 `SERVICE_NAME`）
- 对每个月份执行最终差异同步（`/MIR`）
- 将 `D:\DataPkgFile\yyyy-MM` 重命名为 `yyyy-MM_old`
- 创建联接 `D:\DataPkgFile\yyyy-MM -> E/F:\DataPkgFile\yyyy-MM`
- 记录验证日志（robocopy `/L` 无差异即视为一致）
- 可选抽样哈希验证
- 保留 `_old` 目录（除非 `DELETE_OLD=1`）
- 启动服务（若配置了 `SERVICE_NAME`）

### 步骤 6：一键执行（可选）
```batch
60-run-all.bat
```

该脚本会自动执行步骤 3-5 的完整流程。

---

## 3. 配置详解

### 3.1 分配策略

#### BALANCE_BY_MONTH（奇偶月均衡）
- 奇数月（01、03、05、07、09、11）：优先分配到 E 盘
- 偶数月（02、04、06、08、10、12）：优先分配到 F 盘
- 当首选盘空间不足时，自动切换到另一盘

#### FILL_E_THEN_F（优先填满 E 盘）
- 优先使用 E 盘，直到空间不足
- E 盘空间不足时，切换到 F 盘

### 3.2 安全参数

```batch
REM 预留容量（GB）
set "TARGET_E_MIN_FREE_GB=50"
set "TARGET_F_MIN_FREE_GB=50"

REM 多线程与重试
set "THREADS=16"
set "ROBO_RETRY=1"
set "ROBO_WAIT=2"

REM 抽样验证
set "VERIFY_SAMPLE_COUNT=5"
```

### 3.3 日志管理

所有日志文件保存在 `D:\DataPkg-Migrate\logs\` 目录：
- `pre_*.log`：预同步日志
- `final_*.log`：最终同步日志
- `verify_*.log`：验证日志
- `hash_*.log`：哈希校验日志
- `rollback_*.log`：回滚日志

---

## 4. 回滚方案

### 4.1 快速回滚（仍保留 `_old` 时）
```batch
40-rollback-month.bat YYYY-MM
```

**操作：**
- 删除 `D:\DataPkgFile\YYYY-MM` 联接
- 将 `YYYY-MM_old` 还原为 `YYYY-MM`

### 4.2 深度回滚（`_old` 已删除时）
脚本会自动从 `E/F:\DataPkgFile\YYYY-MM` 回复制到 `D:\DataPkgFile\YYYY-MM`（耗时长）。

---

## 5. 自动化功能

### 5.1 每月自动联接
```batch
50-autolink-schedule.bat
```

**功能：**
- 创建计划任务 `CreateMonthlyLink-DataPkg`
- 每月 1 日 00:05 自动执行
- 为下个月份创建目录联接

### 5.2 手动创建下月联接
```batch
create_current_month_link.bat
```

**功能：**
- 手动为下个月份创建目录联接
- 自动选择可用目标盘（E 或 F）

---

## 6. 验证与监控

### 6.1 切换后验证
1. **应用层验证**：上传/下载抽样测试；查看应用日志无错误
2. **磁盘验证**：D 盘空间是否释放，E/F 盘增长是否合理
3. **备份验证**：次日检查从 D 到 G 的备份日志与容量变化
4. **联接验证**：使用 `dir` 命令检查目录是否为 `<JUNCTION>` 类型

### 6.2 命令速查

```batch
REM 查看可用空间
fsutil volume diskfree E:
wmic logicaldisk where DeviceID='E:' get Size,FreeSpace

REM 查看目录联接
dir D:\DataPkgFile\2022-07

REM 删除联接
rmdir "D:\DataPkgFile\2022-07"

REM 差异预览（不执行复制）
robocopy "源" "目标" /L /NJH /NJS /NP /NDL /NFL
```

---

## 7. 常见问题与解决方案

### 7.1 权限问题
**问题：** 执行 `mklink /J` 报"权限不足"
**解决：** 以管理员身份运行 CMD；或在本地安全策略中允许创建符号链接

### 7.2 Robocopy 返回码
**说明：** Robocopy 的返回码不是标准错误码
- `0/1`：通常表示成功（有无复制差异）
- `>=8`：常表示出错，请检查日志

### 7.3 备份影响
**说明：** 路径仍是 `D:\DataPkgFile`，链接透明；只需确保备份脚本不使用 `/SL`

### 7.4 当前月迁移
**建议：** 不建议迁移当前月与上月（数据写入频繁）；除非确保停写，且窗口足够

---

## 8. 最佳实践

### 8.1 执行顺序
1. **先演练**：`DRY_RUN=1` 多跑几次，观察日志与磁盘变化
2. **先小后大**：先迁移一个小月；通过后批量推进
3. **留足余量**：E/F 每盘至少保留 10-15% 剩余空间
4. **保留回滚**：在确认稳定运行前，务必暂不删除 `_old`
5. **记录日志**：所有脚本日志集中到 `D:\DataPkg-Migrate\logs\`，便于审计与追踪

### 8.2 稳定后清理
确认稳定运行 1-7 天后，将 `DELETE_OLD=1` 执行一次 `30-cutover-month.bat`，清理 `_old` 目录释放空间。

---

## 9. 脚本依赖与兼容性

### 9.1 必需命令
- `robocopy`：文件复制（Windows 内置）
- `mklink`：创建目录联接（需要管理员权限）
- `wmic`：磁盘空间查询
- `schtasks`：计划任务管理

### 9.2 可选命令
- `powershell`：增强的月份处理和数字计算
- `certutil`：文件哈希校验

### 9.3 兼容性
- **Windows Server 2008 R2**：完全兼容
- **Windows Server 2012+**：完全兼容，性能更佳
- **Windows 10/11**：完全兼容

---

## 10. 故障排除

### 10.1 日志分析
检查 `D:\DataPkg-Migrate\logs\` 目录下的日志文件：
- 查看 `pre_*.log` 了解预同步状态
- 查看 `final_*.log` 了解最终同步结果
- 查看 `verify_*.log` 了解验证结果

### 10.2 常见错误
1. **目录不存在**：检查源目录路径是否正确
2. **权限不足**：确保以管理员身份运行
3. **磁盘空间不足**：检查目标盘剩余空间
4. **服务停止失败**：检查服务名是否正确

### 10.3 紧急恢复
如遇严重问题，可立即执行回滚：
```batch
40-rollback-month.bat YYYY-MM
```

---

## 11. 总结

本方案通过 NTFS 目录联接技术，实现了数据迁移的透明化，具有以下优势：

1. **业务透明**：应用代码无需修改，读写路径保持不变
2. **备份兼容**：现有备份任务无需调整
3. **风险可控**：支持回滚，保留原数据
4. **自动化程度高**：提供完整的脚本化解决方案
5. **验证完善**：多重验证机制确保数据一致性

通过遵循本执行步骤，可以安全、高效地完成数据迁移，释放 D 盘空间，同时保持业务的连续性和稳定性。


