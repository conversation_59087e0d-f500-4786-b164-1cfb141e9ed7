package com.cirpoint.datapkg.service;

import com.cirpoint.datapkg.config.DataPkgMigrateConfig;
import com.cirpoint.datapkg.util.FileSystemUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.List;

/**
 * 月份清单生成服务
 * 对应原脚本：10-list-months.bat
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MonthListService {
    
    private final DataPkgMigrateConfig config;
    private final FileSystemUtils fileSystemUtils;
    
    /**
     * 生成月份清单文件
     * 
     * @return 生成结果
     */
    public MonthListResult generateMonthList() {
        log.info("开始生成月份清单");
        
        try {
            // 扫描所有月份目录
            List<String> allMonths = fileSystemUtils.scanMonthDirectories(config.getSourceRootPath());
            log.info("扫描到 {} 个月份目录", allMonths.size());
            
            if (allMonths.isEmpty()) {
                log.warn("未找到任何月份目录");
                return MonthListResult.builder()
                        .success(false)
                        .message("未找到任何月份目录")
                        .build();
            }
            
            // 过滤月份，排除最近N个月
            List<String> monthsToMigrate = fileSystemUtils.filterMonths(allMonths, config.getExcludeRecentMonths());
            log.info("排除最近 {} 个月后，待迁移月份数: {}", config.getExcludeRecentMonths(), monthsToMigrate.size());
            
            // 写入文件
            Path baseDir = config.getSourceRootPath().getParent();
            Path allMonthsFile = baseDir.resolve("months.txt");
            Path migrateMonthsFile = baseDir.resolve("months_to_migrate.txt");
            
            // 写入所有月份文件
            Files.write(allMonthsFile, allMonths, StandardOpenOption.CREATE, StandardOpenOption.WRITE);
            log.info("已生成所有月份清单文件: {}", allMonthsFile);
            
            // 写入待迁移月份文件
            Files.write(migrateMonthsFile, monthsToMigrate, StandardOpenOption.CREATE, StandardOpenOption.WRITE);
            log.info("已生成待迁移月份清单文件: {}", migrateMonthsFile);
            
            return MonthListResult.builder()
                    .success(true)
                    .message("月份清单生成成功")
                    .allMonthsFile(allMonthsFile.toString())
                    .migrateMonthsFile(migrateMonthsFile.toString())
                    .totalMonths(allMonths.size())
                    .migrateMonths(monthsToMigrate.size())
                    .excludedMonths(config.getExcludeRecentMonths())
                    .allMonthsList(allMonths)
                    .migrateMonthsList(monthsToMigrate)
                    .build();
                    
        } catch (IOException e) {
            log.error("生成月份清单失败", e);
            return MonthListResult.builder()
                    .success(false)
                    .message("生成月份清单失败: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 读取待迁移月份清单
     * 
     * @return 月份列表
     */
    public List<String> readMigrateMonthsList() {
        Path baseDir = config.getSourceRootPath().getParent();
        Path migrateMonthsFile = baseDir.resolve("months_to_migrate.txt");
        
        if (!Files.exists(migrateMonthsFile)) {
            log.warn("待迁移月份清单文件不存在: {}", migrateMonthsFile);
            return List.of();
        }
        
        try {
            List<String> months = Files.readAllLines(migrateMonthsFile);
            log.info("读取到 {} 个待迁移月份", months.size());
            return months;
        } catch (IOException e) {
            log.error("读取待迁移月份清单失败: {}", migrateMonthsFile, e);
            return List.of();
        }
    }
    
    /**
     * 检查月份清单文件是否存在
     * 
     * @return 检查结果
     */
    public boolean checkMonthListFiles() {
        Path baseDir = config.getSourceRootPath().getParent();
        Path allMonthsFile = baseDir.resolve("months.txt");
        Path migrateMonthsFile = baseDir.resolve("months_to_migrate.txt");
        
        boolean allExists = Files.exists(allMonthsFile);
        boolean migrateExists = Files.exists(migrateMonthsFile);
        
        log.info("月份清单文件检查 - months.txt: {}, months_to_migrate.txt: {}", 
                allExists ? "存在" : "不存在", migrateExists ? "存在" : "不存在");
        
        return allExists && migrateExists;
    }
    
    /**
     * 月份清单生成结果
     */
    @lombok.Builder
    @lombok.Data
    public static class MonthListResult {
        private boolean success;
        private String message;
        private String allMonthsFile;
        private String migrateMonthsFile;
        private int totalMonths;
        private int migrateMonths;
        private int excludedMonths;
        private List<String> allMonthsList;
        private List<String> migrateMonthsList;
        
        /**
         * 获取结果摘要
         */
        public String getSummary() {
            if (!success) {
                return "失败: " + message;
            }
            
            return String.format(
                "月份清单生成成功:\n" +
                "  总月份数: %d\n" +
                "  待迁移月份数: %d\n" +
                "  排除最近: %d个月\n" +
                "  所有月份文件: %s\n" +
                "  待迁移月份文件: %s",
                totalMonths, migrateMonths, excludedMonths,
                allMonthsFile, migrateMonthsFile
            );
        }
    }
}
