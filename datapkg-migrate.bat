@echo off
REM 数据包迁移工具启动脚本
REM 作者: wanghq
REM 日期: 2025-08-27

chcp 65001 >nul 2>nul
setlocal ENABLEDELAYEDEXPANSION

REM 设置Java程序路径
set "JAR_FILE=file-handle.jar"
set "JAVA_OPTS=-Xms512m -Xmx2g -Dspring.profiles.active=datapkg"

REM 检查Java环境
java -version >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装Java 8或更高版本
    pause
    exit /b 1
)

REM 检查JAR文件
if not exist "%JAR_FILE%" (
    echo 错误: 未找到JAR文件 %JAR_FILE%
    echo 请确保文件存在于当前目录中
    pause
    exit /b 1
)

REM 如果没有参数，显示帮助信息
if "%~1"=="" (
    java %JAVA_OPTS% -jar "%JAR_FILE%" help
    pause
    exit /b 0
)

REM 执行Java程序
echo 执行命令: java %JAVA_OPTS% -jar "%JAR_FILE%" %*
echo.
java %JAVA_OPTS% -jar "%JAR_FILE%" %*

REM 检查执行结果
if %errorlevel% equ 0 (
    echo.
    echo 命令执行成功
) else (
    echo.
    echo 命令执行失败，退出码: %errorlevel%
)

pause
exit /b %errorlevel%
