package com.cirpoint.datapkg.service;

import com.cirpoint.datapkg.config.DataPkgMigrateConfig;
import com.cirpoint.datapkg.util.FileSystemUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.file.Files;
import java.nio.file.Path;

/**
 * 回滚服务
 * 对应原脚本：40-rollback-month.bat
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RollbackService {
    
    private final DataPkgMigrateConfig config;
    private final FileSystemUtils fileSystemUtils;
    
    /**
     * 回滚指定月份
     * 
     * @param month 月份（格式：YYYY-MM）
     * @return 回滚结果
     */
    public RollbackResult rollbackMonth(String month) {
        log.info("开始回滚月份: {}", month);
        
        if (month == null || month.trim().isEmpty()) {
            return RollbackResult.builder()
                    .success(false)
                    .message("月份参数不能为空")
                    .build();
        }
        
        // 停止服务
        boolean serviceStopSuccess = true;
        if (!config.getServiceName().isEmpty()) {
            serviceStopSuccess = fileSystemUtils.manageService(config.getServiceName(), "stop", config.isDryRun());
            if (!serviceStopSuccess) {
                log.error("停止服务失败，但继续执行回滚");
            }
        }
        
        Path sourceDir = config.getSourceRootPath().resolve(month);
        Path oldDir = config.getSourceRootPath().resolve(month + "_old");
        
        boolean rollbackSuccess;
        String rollbackType;
        String message;
        
        if (Files.exists(oldDir)) {
            // 快速回滚：使用_old目录
            rollbackSuccess = performQuickRollback(sourceDir, oldDir, month);
            rollbackType = "快速回滚";
            message = rollbackSuccess ? "快速回滚成功" : "快速回滚失败";
        } else {
            // 深度回滚：从目标盘复制回来
            rollbackSuccess = performDeepRollback(sourceDir, month);
            rollbackType = "深度回滚";
            message = rollbackSuccess ? "深度回滚成功" : "深度回滚失败";
        }
        
        // 启动服务
        boolean serviceStartSuccess = true;
        if (!config.getServiceName().isEmpty()) {
            serviceStartSuccess = fileSystemUtils.manageService(config.getServiceName(), "start", config.isDryRun());
            if (!serviceStartSuccess) {
                log.error("启动服务失败");
            }
        }
        
        boolean overallSuccess = rollbackSuccess && serviceStartSuccess;
        String finalMessage = String.format("%s，服务启动: %s", message, serviceStartSuccess ? "成功" : "失败");
        
        log.info("月份 {} 回滚完成: {}", month, finalMessage);
        
        return RollbackResult.builder()
                .success(overallSuccess)
                .message(finalMessage)
                .month(month)
                .rollbackType(rollbackType)
                .rollbackSuccess(rollbackSuccess)
                .serviceStopSuccess(serviceStopSuccess)
                .serviceStartSuccess(serviceStartSuccess)
                .build();
    }
    
    /**
     * 执行快速回滚（使用_old目录）
     * 
     * @param sourceDir 源目录
     * @param oldDir _old目录
     * @param month 月份
     * @return 是否成功
     */
    private boolean performQuickRollback(Path sourceDir, Path oldDir, String month) {
        log.info("执行快速回滚: {} -> {}", oldDir, sourceDir);
        
        // 1. 删除目录联接
        if (Files.exists(sourceDir)) {
            boolean removeSuccess = fileSystemUtils.removeJunction(sourceDir, config.isDryRun());
            if (!removeSuccess) {
                log.error("删除目录联接失败: {}", sourceDir);
                return false;
            }
        }
        
        // 2. 还原_old目录
        boolean renameSuccess = fileSystemUtils.renameDirectory(oldDir, sourceDir, config.isDryRun());
        if (!renameSuccess) {
            log.error("还原目录失败: {} -> {}", oldDir, sourceDir);
            return false;
        }
        
        log.info("快速回滚成功: {}", month);
        return true;
    }
    
    /**
     * 执行深度回滚（从目标盘复制）
     * 
     * @param sourceDir 源目录
     * @param month 月份
     * @return 是否成功
     */
    private boolean performDeepRollback(Path sourceDir, String month) {
        log.info("执行深度回滚（从目标盘复制）: {}", month);
        
        // 查找目标目录
        Path targetDir = findTargetDirectory(month);
        if (targetDir == null) {
            log.error("在 E: 和 F: 盘都未找到 {} 目录", month);
            return false;
        }
        
        log.info("找到目标目录: {}", targetDir);
        
        // 删除现有的联接或目录
        if (Files.exists(sourceDir)) {
            if (fileSystemUtils.isJunction(sourceDir)) {
                boolean removeSuccess = fileSystemUtils.removeJunction(sourceDir, config.isDryRun());
                if (!removeSuccess) {
                    log.error("删除目录联接失败: {}", sourceDir);
                    return false;
                }
            } else {
                boolean deleteSuccess = fileSystemUtils.deleteDirectory(sourceDir, config.isDryRun());
                if (!deleteSuccess) {
                    log.error("删除目录失败: {}", sourceDir);
                    return false;
                }
            }
        }
        
        // 从目标盘复制回源盘
        String options = String.format(
                "/MIR /COPY:DATSO /DCOPY:DAT /R:%d /W:%d /MT:%d /FFT /XJ /TEE",
                config.getRoboRetry(), config.getRoboWait(), config.getThreads()
        );
        
        Path logFile = config.getLogDirPath().resolve("rollback_" + month + ".log");
        
        boolean copySuccess = fileSystemUtils.robocopy(targetDir, sourceDir, options, logFile, config.isDryRun());
        if (!copySuccess) {
            log.error("从目标盘复制失败: {} -> {}", targetDir, sourceDir);
            return false;
        }
        
        log.info("深度回滚成功: {}", month);
        return true;
    }
    
    /**
     * 查找目标目录
     * 
     * @param month 月份
     * @return 目标目录路径，未找到返回null
     */
    private Path findTargetDirectory(String month) {
        // 先检查E盘
        Path ePath = config.getTargetEPath().resolve(month);
        if (Files.exists(ePath)) {
            log.info("在E盘找到目录: {}", ePath);
            return ePath;
        }
        
        // 再检查F盘
        Path fPath = config.getTargetFPath().resolve(month);
        if (Files.exists(fPath)) {
            log.info("在F盘找到目录: {}", fPath);
            return fPath;
        }
        
        return null;
    }
    
    /**
     * 检查月份是否可以回滚
     * 
     * @param month 月份
     * @return 检查结果
     */
    public RollbackCheckResult checkRollbackAvailable(String month) {
        if (month == null || month.trim().isEmpty()) {
            return RollbackCheckResult.builder()
                    .canRollback(false)
                    .message("月份参数不能为空")
                    .build();
        }
        
        Path sourceDir = config.getSourceRootPath().resolve(month);
        Path oldDir = config.getSourceRootPath().resolve(month + "_old");
        
        boolean hasOldDir = Files.exists(oldDir);
        boolean hasTargetDir = findTargetDirectory(month) != null;
        boolean isJunction = Files.exists(sourceDir) && fileSystemUtils.isJunction(sourceDir);
        
        String rollbackType;
        boolean canRollback;
        String message;
        
        if (hasOldDir) {
            rollbackType = "快速回滚";
            canRollback = true;
            message = "可以使用_old目录进行快速回滚";
        } else if (hasTargetDir) {
            rollbackType = "深度回滚";
            canRollback = true;
            message = "可以从目标盘进行深度回滚（耗时较长）";
        } else {
            rollbackType = "无法回滚";
            canRollback = false;
            message = "既没有_old目录，也没有找到目标盘数据";
        }
        
        return RollbackCheckResult.builder()
                .canRollback(canRollback)
                .rollbackType(rollbackType)
                .message(message)
                .month(month)
                .hasOldDir(hasOldDir)
                .hasTargetDir(hasTargetDir)
                .isJunction(isJunction)
                .build();
    }
    
    /**
     * 回滚结果
     */
    @lombok.Builder
    @lombok.Data
    public static class RollbackResult {
        private boolean success;
        private String message;
        private String month;
        private String rollbackType;
        private boolean rollbackSuccess;
        private boolean serviceStopSuccess;
        private boolean serviceStartSuccess;
        
        /**
         * 获取结果摘要
         */
        public String getSummary() {
            return String.format(
                "回滚结果 - 月份: %s, 类型: %s, 结果: %s\n" +
                "服务管理 - 停止: %s, 启动: %s\n" +
                "详细信息: %s",
                month, rollbackType, rollbackSuccess ? "成功" : "失败",
                serviceStopSuccess ? "成功" : "失败",
                serviceStartSuccess ? "成功" : "失败",
                message
            );
        }
    }
    
    /**
     * 回滚检查结果
     */
    @lombok.Builder
    @lombok.Data
    public static class RollbackCheckResult {
        private boolean canRollback;
        private String rollbackType;
        private String message;
        private String month;
        private boolean hasOldDir;
        private boolean hasTargetDir;
        private boolean isJunction;
        
        /**
         * 获取检查摘要
         */
        public String getSummary() {
            return String.format(
                "回滚检查 - 月份: %s\n" +
                "可回滚: %s\n" +
                "回滚类型: %s\n" +
                "状态: _old目录=%s, 目标盘数据=%s, 当前是联接=%s\n" +
                "说明: %s",
                month, canRollback ? "是" : "否", rollbackType,
                hasOldDir ? "存在" : "不存在",
                hasTargetDir ? "存在" : "不存在",
                isJunction ? "是" : "否",
                message
            );
        }
    }
}
