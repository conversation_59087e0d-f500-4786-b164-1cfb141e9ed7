#!/bin/bash
# 数据包迁移工具启动脚本
# 作者: wanghq
# 日期: 2025-08-27

# 设置Java程序路径
JAR_FILE="file-handle.jar"
JAVA_OPTS="-Xms512m -Xmx2g -Dspring.profiles.active=datapkg"

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请确保已安装Java 8或更高版本"
    exit 1
fi

# 检查JAR文件
if [ ! -f "$JAR_FILE" ]; then
    echo "错误: 未找到JAR文件 $JAR_FILE"
    echo "请确保文件存在于当前目录中"
    exit 1
fi

# 如果没有参数，显示帮助信息
if [ $# -eq 0 ]; then
    java $JAVA_OPTS -jar "$JAR_FILE" help
    exit 0
fi

# 执行Java程序
echo "执行命令: java $JAVA_OPTS -jar $JAR_FILE $*"
echo
java $JAVA_OPTS -jar "$JAR_FILE" "$@"

# 检查执行结果
exit_code=$?
if [ $exit_code -eq 0 ]; then
    echo
    echo "命令执行成功"
else
    echo
    echo "命令执行失败，退出码: $exit_code"
fi

exit $exit_code
