package com.cirpoint.datapkg.service;

import com.cirpoint.datapkg.config.DataPkgMigrateConfig;
import com.cirpoint.datapkg.util.FileSystemUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * 切换服务
 * 对应原脚本：30-cutover-month.bat
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CutoverService {
    
    private final DataPkgMigrateConfig config;
    private final FileSystemUtils fileSystemUtils;
    private final MonthListService monthListService;
    private final PreSyncService preSyncService;
    
    /**
     * 执行切换操作
     * 
     * @return 切换结果
     */
    public CutoverResult executeCutover() {
        log.info("开始执行切换操作");
        
        // 检查待迁移月份清单文件
        if (!monthListService.checkMonthListFiles()) {
            return CutoverResult.builder()
                    .success(false)
                    .message("未找到月份清单文件，请先运行月份清单生成")
                    .build();
        }
        
        // 读取待迁移月份列表
        List<String> monthsToMigrate = monthListService.readMigrateMonthsList();
        if (monthsToMigrate.isEmpty()) {
            return CutoverResult.builder()
                    .success(false)
                    .message("待迁移月份列表为空")
                    .build();
        }
        
        log.info("即将执行最终差异同步与切换，请确保业务已停止写入");
        log.info("待切换月份数: {}", monthsToMigrate.size());
        
        // 停止服务
        boolean serviceStopSuccess = true;
        if (!config.getServiceName().isEmpty()) {
            serviceStopSuccess = fileSystemUtils.manageService(config.getServiceName(), "stop", config.isDryRun());
            if (!serviceStopSuccess) {
                log.error("停止服务失败，中止切换操作");
                return CutoverResult.builder()
                        .success(false)
                        .message("停止服务失败: " + config.getServiceName())
                        .build();
            }
        }
        
        List<MonthCutoverResult> results = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;
        boolean overallSuccess = true;
        
        // 逐个处理月份
        for (String month : monthsToMigrate) {
            MonthCutoverResult result = cutoverMonth(month);
            results.add(result);
            
            if (result.isSuccess()) {
                successCount++;
            } else {
                failCount++;
                overallSuccess = false;
                log.error("月份 {} 切换失败，继续处理其他月份", month);
            }
        }
        
        // 启动服务
        boolean serviceStartSuccess = true;
        if (!config.getServiceName().isEmpty()) {
            serviceStartSuccess = fileSystemUtils.manageService(config.getServiceName(), "start", config.isDryRun());
            if (!serviceStartSuccess) {
                log.error("启动服务失败");
            }
        }
        
        String message = String.format("切换完成 - 成功: %d, 失败: %d, 服务启动: %s", 
                successCount, failCount, serviceStartSuccess ? "成功" : "失败");
        
        log.info(message);
        
        return CutoverResult.builder()
                .success(overallSuccess && serviceStartSuccess)
                .message(message)
                .totalMonths(monthsToMigrate.size())
                .successCount(successCount)
                .failCount(failCount)
                .serviceStopSuccess(serviceStopSuccess)
                .serviceStartSuccess(serviceStartSuccess)
                .monthResults(results)
                .build();
    }
    
    /**
     * 切换单个月份
     * 
     * @param month 月份
     * @return 切换结果
     */
    private MonthCutoverResult cutoverMonth(String month) {
        log.info("切换月份: {}", month);
        
        Path sourceDir = config.getSourceRootPath().resolve(month);
        if (!Files.exists(sourceDir)) {
            log.warn("源目录不存在，跳过: {}", sourceDir);
            return MonthCutoverResult.builder()
                    .month(month)
                    .success(false)
                    .message("源目录不存在")
                    .build();
        }
        
        // 选择目标盘
        PreSyncService.TargetSelection targetSelection = preSyncService.selectTarget(month);
        if (targetSelection.getTargetPath() == null) {
            log.error("未找到可用目标盘，跳过月份: {}", month);
            return MonthCutoverResult.builder()
                    .month(month)
                    .success(false)
                    .message("未找到可用目标盘")
                    .build();
        }
        
        Path targetDir = targetSelection.getTargetPath().resolve(month);
        Path oldDir = config.getSourceRootPath().resolve(month + "_old");
        
        // 检查是否存在残留的_old目录
        if (Files.exists(oldDir)) {
            log.error("发现残留 _old 目录，请先处理: {}", oldDir);
            return MonthCutoverResult.builder()
                    .month(month)
                    .success(false)
                    .message("发现残留 _old 目录: " + oldDir)
                    .build();
        }
        
        // 1. 最终差异同步
        boolean syncSuccess = performFinalSync(sourceDir, targetDir, month);
        if (!syncSuccess) {
            return MonthCutoverResult.builder()
                    .month(month)
                    .success(false)
                    .message("最终同步失败")
                    .sourceDir(sourceDir.toString())
                    .targetDir(targetDir.toString())
                    .build();
        }
        
        // 2. 重命名原目录为_old
        boolean renameSuccess = fileSystemUtils.renameDirectory(sourceDir, oldDir, config.isDryRun());
        if (!renameSuccess) {
            return MonthCutoverResult.builder()
                    .month(month)
                    .success(false)
                    .message("重命名目录失败")
                    .sourceDir(sourceDir.toString())
                    .targetDir(targetDir.toString())
                    .build();
        }
        
        // 3. 创建目录联接
        boolean junctionSuccess = fileSystemUtils.createJunction(sourceDir, targetDir, config.isDryRun());
        if (!junctionSuccess) {
            // 回滚：恢复原目录名
            fileSystemUtils.renameDirectory(oldDir, sourceDir, config.isDryRun());
            return MonthCutoverResult.builder()
                    .month(month)
                    .success(false)
                    .message("创建目录联接失败")
                    .sourceDir(sourceDir.toString())
                    .targetDir(targetDir.toString())
                    .build();
        }
        
        // 4. 验证同步结果
        boolean verifySuccess = performVerification(sourceDir, targetDir, month);
        
        // 5. 抽样哈希验证
        boolean hashVerifySuccess = performHashVerification(sourceDir, month);
        
        // 6. 可选删除_old目录
        boolean deleteOldSuccess = true;
        if (config.isDeleteOld()) {
            deleteOldSuccess = fileSystemUtils.deleteDirectory(oldDir, config.isDryRun());
            if (!deleteOldSuccess) {
                log.warn("删除旧目录失败，但不影响整体结果: {}", oldDir);
            }
        } else {
            log.info("已保留旧目录（回滚窗口期内可随时恢复）: {}", oldDir);
        }
        
        boolean overallSuccess = syncSuccess && renameSuccess && junctionSuccess && verifySuccess && hashVerifySuccess;
        String message = overallSuccess ? "切换成功" : "切换部分失败";
        
        log.info("月份 {} 切换结果: {}", month, message);
        
        return MonthCutoverResult.builder()
                .month(month)
                .success(overallSuccess)
                .message(message)
                .sourceDir(sourceDir.toString())
                .targetDir(targetDir.toString())
                .targetDrive(targetSelection.getDrive())
                .syncSuccess(syncSuccess)
                .renameSuccess(renameSuccess)
                .junctionSuccess(junctionSuccess)
                .verifySuccess(verifySuccess)
                .hashVerifySuccess(hashVerifySuccess)
                .deleteOldSuccess(deleteOldSuccess)
                .oldDirDeleted(config.isDeleteOld())
                .build();
    }
    
    /**
     * 执行最终差异同步
     */
    private boolean performFinalSync(Path sourceDir, Path targetDir, String month) {
        log.info("执行最终差异同步: {} -> {}", sourceDir, targetDir);
        
        // 构建Robocopy选项（使用/MIR进行镜像同步）
        String options = String.format(
                "/MIR /COPY:DATSO /DCOPY:DAT /R:%d /W:%d /MT:%d /FFT /XJ /TEE",
                config.getRoboRetry(), config.getRoboWait(), config.getThreads()
        );
        
        // 日志文件
        Path logFile = config.getLogDirPath().resolve("final_" + month + ".log");
        
        // 执行Robocopy
        return fileSystemUtils.robocopy(sourceDir, targetDir, options, logFile, config.isDryRun());
    }
    
    /**
     * 执行验证
     */
    private boolean performVerification(Path sourceDir, Path targetDir, String month) {
        log.info("执行同步验证: {}", sourceDir);
        
        // 使用Robocopy /L参数进行差异预览验证
        String options = "/L /NJH /NJS /NP /NDL /NFL /R:0";
        Path logFile = config.getLogDirPath().resolve("verify_" + month + ".log");
        
        return fileSystemUtils.robocopy(sourceDir, targetDir, options, logFile, config.isDryRun());
    }
    
    /**
     * 执行抽样哈希验证
     */
    private boolean performHashVerification(Path sourceDir, String month) {
        if (config.getVerifySampleCount() <= 0) {
            log.info("跳过哈希验证，抽样数量为0");
            return true;
        }
        
        log.info("执行抽样哈希验证: {}, 抽样数量: {}", sourceDir, config.getVerifySampleCount());
        
        Path logFile = config.getLogDirPath().resolve("hash_" + month + ".log");
        
        if (config.isDryRun()) {
            log.info("[演练模式] 将执行抽样哈希验证");
            return true;
        }
        
        return fileSystemUtils.sampleHashVerify(sourceDir, config.getVerifySampleCount(), logFile);
    }

    /**
     * 月份切换结果
     */
    @lombok.Builder
    @lombok.Data
    public static class MonthCutoverResult {
        private String month;
        private boolean success;
        private String message;
        private String sourceDir;
        private String targetDir;
        private String targetDrive;
        private boolean syncSuccess;
        private boolean renameSuccess;
        private boolean junctionSuccess;
        private boolean verifySuccess;
        private boolean hashVerifySuccess;
        private boolean deleteOldSuccess;
        private boolean oldDirDeleted;
    }

    /**
     * 切换结果
     */
    @lombok.Builder
    @lombok.Data
    public static class CutoverResult {
        private boolean success;
        private String message;
        private int totalMonths;
        private int successCount;
        private int failCount;
        private boolean serviceStopSuccess;
        private boolean serviceStartSuccess;
        private List<MonthCutoverResult> monthResults;

        /**
         * 获取结果摘要
         */
        public String getSummary() {
            StringBuilder sb = new StringBuilder();
            sb.append(String.format("切换操作完成 - 总计: %d, 成功: %d, 失败: %d\n",
                    totalMonths, successCount, failCount));
            sb.append(String.format("服务管理 - 停止: %s, 启动: %s\n",
                    serviceStopSuccess ? "成功" : "失败",
                    serviceStartSuccess ? "成功" : "失败"));

            if (monthResults != null) {
                for (MonthCutoverResult result : monthResults) {
                    sb.append(String.format("  %s: %s -> %s\n",
                            result.getMonth(),
                            result.isSuccess() ? "成功" : "失败",
                            result.getTargetDrive() != null ? result.getTargetDrive() : "无"));

                    if (!result.isSuccess()) {
                        sb.append(String.format("    错误: %s\n", result.getMessage()));
                    } else {
                        sb.append(String.format("    同步: %s, 重命名: %s, 联接: %s, 验证: %s, 哈希: %s\n",
                                result.isSyncSuccess() ? "✓" : "✗",
                                result.isRenameSuccess() ? "✓" : "✗",
                                result.isJunctionSuccess() ? "✓" : "✗",
                                result.isVerifySuccess() ? "✓" : "✗",
                                result.isHashVerifySuccess() ? "✓" : "✗"));
                    }
                }
            }

            return sb.toString();
        }
    }
}
